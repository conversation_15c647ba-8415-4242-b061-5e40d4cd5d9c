{
  "permissions": {
    "allow": [
      "Bash(*)",
      "Read(*)",
      "Write(*)",
      "Edit(*)",
      "MultiEdit(*)",
      "Glob(*)",
      "Grep(*)",
      "LS(*)",
      "NotebookEdit(*)",
      "NotebookRead(*)",
      "WebFetch(*)",
      "WebSearch(*)",
      "TodoRead",
      "TodoWrite",
      "Agent(*)"
    ],
    "deny": []
  },
  "env": {
    "BASH_DEFAULT_TIMEOUT_MS": "600000",
    "BASH_MAX_TIMEOUT_MS": "800000",
    "BASH_MAX_OUTPUT_LENGTH": "800000",
    "CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR": "true",
    "MAX_THINKING_TOKENS": "20000",
    "MCP_TIMEOUT": "90000",
    "MCP_TOOL_TIMEOUT": "160000",
    "DISABLE_COST_WARNINGS": "1",
    "DISABLE_NON_ESSENTIAL_MODEL_CALLS": "0",
    "CLAUDE_CODE_MAX_OUTPUT_TOKENS": "32000"
  },
  "cleanupPeriodDays": 90,
  "includeCoAuthoredBy": true,
  "automation": {
    "enabled": true,
    "defaultTimeout": 600000,
    "maxRetries": 3,
    "retryBackoff": 2000,
    "parallelExecution": true,
    "batchOperations": true,
    "autoSaveMemory": true,
    "autoCommit": false
  },
    "memoryDefaults": {
      "maxSize": "5GB",
      "autoCompress": true,
      "autoCleanup": true,
      "indexingEnabled": true,
      "persistenceEnabled": true
    }
  }
}