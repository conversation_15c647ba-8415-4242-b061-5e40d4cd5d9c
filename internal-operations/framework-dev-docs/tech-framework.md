# Claude‑Flow Rust Stack — Technical Documentation Framework

> **Purpose**  A living blueprint—extensible by human or AI agents—for designing, building and operating a Claude‑Flow multi‑agent platform on a lean Rust foundation.
>
> **Audience**  Backend engineers, DevOps/SRE, agent‑orchestrator authors, and documentation‑generation agents.

---

## 1 System Overview

* **Problem Domain** Multi‑agent code‑automation driven by Claude Code CLI, orchestrated by a Rust service.
* **Objectives** Massive concurrency, minimal operational footprint, rapid agent spawn/respawn, and first‑class observability.
* **Primary Components** Rust orchestrator, Claude C<PERSON> agents, NATS (JetStream) bus, PostgreSQL, OpenTelemetry stack.

> *Placeholder:* add short elevator pitch and architecture diagram link when available.

---

## 2 High‑Level Architecture Diagram *(placeholder)*

```
+-------------+   spawn   +--------------+   pub/sub   +-----------+
| VS Code /   |──────────▶| Orchestrator |────────────▶|  NATS Bus |
| Terminal    |           |   (Rust)     |◀────────────|           |
+-------------+           +--------------+ events/ctrl +-----------+
                                │                        │
                                │ SQLx                   │ OTLP
                                ▼                        ▼
                         +--------------+        +----------------+
                         |  PostgreSQL  |        | Trace Backend  |
                         +--------------+        +----------------+
```

---

## 3 Runtime & Concurrency

| Topic         | Choice                                   | Notes                                                                               |
| ------------- | ---------------------------------------- | ----------------------------------------------------------------------------------- |
| Async runtime | **Tokio 1.38**                           | `full`, `process`, `tracing` features; single event loop for I/O + child processes. |
| Supervision   | In‑house restart loop (opt. **Bastion**) | ≈50 LOC monitor; can evolve into full supervisor tree later.                        |
| Scheduling    | Tokio work‑stealing scheduler            | Scales to hundreds of agents per core.                                              |

---

## 4 Transport Layer

### 4.1 HTTP / REST

* **Framework** Axum 0.8
* **Endpoints** `/healthz` (liveness) · `/spawn` (programmatic agent spawn) · `/metrics` (Prometheus)

### 4.2 gRPC

* **Library** Tonic 0.11
* **Services**

  * `AgentManager` — `SpawnAgent`, `KillAgent`, `ListAgents`
  * `EventStream` — bi‑directional bus streaming

---

## 5 Messaging & Eventing

| Purpose           | Subject Convention            | Durability       | Typical Payload                               |
| ----------------- | ----------------------------- | ---------------- | --------------------------------------------- |
| Agent in‑channel  | `group.{gid}.agent.{aid}.in`  | volatile         | commands, data chunks                         |
| Agent out‑channel | `group.{gid}.agent.{aid}.out` | JetStream opt‑in | logs, status, JSON packets                    |
| Group broadcast   | `group.{gid}.broadcast`       | volatile         | shared tool calls, announcements              |
| Control plane     | `group.{gid}.control.*`       | JetStream        | spawn/kill directives, scaling hints          |
| Context request   | `ctx.{gid}.{aid}.get`         | volatile         | `{ "key": "<context‑key>" }`                  |
| Context update    | `ctx.{gid}.{aid}.set`         | JetStream opt‑in | `{ "key": "<context‑key>", "value": <JSON> }` |
| Embedding share   | `ctx.{gid}.embedding`         | JetStream opt‑in | `{ "doc_id": UUID, "embedding": [f32;768] }`  |

**Library** `async‑nats 0.34` with JetStream enabled for control, transcript replay and context/embedding channels.

### 5.1 Real‑Time Context & Embedding Flow

1. **Lookup** Agent publishes `ctx.{gid}.{aid}.get`.
2. **Serve** Orchestrator (or peer agent) pulls value from JetStream KV or Postgres and replies via `ctx.{gid}.{aid}.set`.
3. **Share** Agents publish embeddings to `ctx.{gid}.embedding`; subscribers update in‑memory ANN indices.
4. **Evict** KV TTL expiry emits a `del` event; orchestrator persists the final diff to `agent_state`.

---

## 6 Data & Persistence

| Tier            | Technology                   | Purpose                                                                                    |
| --------------- | ---------------------------- | ------------------------------------------------------------------------------------------ |
| Hot cache       | (opt) **DashMap**            | Micro‑second, in‑process cache for orchestration metadata.                                 |
| Working memory  | **JetStream KV**             | Ephemeral per‑agent/task K/V; TTL‑based cleanup, replicated cluster‑wide.                  |
| Primary store   | **PostgreSQL 15 + SQLx 0.7** | Authoritative records plus session tables `agent_state` & `conversation_history`.          |
| Secondary cache | (opt) **Redis**              | Add only if profiling reveals need for complex data structures or high‑rate rate limiting. |

### 6.1 Schema & Migrations

```sql
-- Persistent per‑agent context
drop table if exists agent_state;
create table agent_state (
  id uuid primary key,
  group_id uuid not null,
  state jsonb not null,
  updated_at timestamp default now()
);

-- Long‑form transcripts
drop table if exists conversation_history;
create table conversation_history (
  id bigserial primary key,
  agent_id uuid references agent_state(id),
  role text not null,
  content text not null,
  ts timestamp default now()
);
```

Manage migrations with `sqlx migrate` in `/migrations`.

### 6.2 Session / Agent Memory Pattern

1. **Read** Orchestrator checks JetStream KV → on miss hydrates from Postgres → caches with TTL.
2. **Live task** Agents read/write context directly in KV (sub‑ms latency).
3. **Flush** On agent exit or TTL expiry, orchestrator writes diff back to `agent_state`.

---

## 7 Persistent Context & Memory

* **Long‑lived state** Stored in Postgres (`agent_state`, `conversation_history`).
* **Ephemeral working memory** Per‑agent JetStream KV buckets (`mem.{agent_id}`) for real‑time context.
* **Live exchange** Context subjects (`ctx.*`) provide request/reply semantics while embeddings broadcast over `ctx.{gid}.embedding`.
* **Cache‑miss flow** Agent → KV → (fallback) Postgres → KV update → live publish.

---

## 8 Process Orchestration & Agent Management

```mermaid
sequenceDiagram
    caller->>Axum POST /spawn: spawn_request
    Axum->>Supervisor: enqueue
    Supervisor->>Tokio::process: spawn("claude …")
    Note over Tokio::process: streams stdout/stderr
    Tokio::process->>NATS: publish agent.out
```

* **Restart policy** Exponential back‑off, max 5 restarts/min.
* **Hierarchical spawning** Parent agent publishes `control.spawn`; orchestrator fulfills and wires new subjects.

---

## 9 Observability

* **Tracing** `tracing` JSON layer → OTLP exporter.
* **Metrics** `metrics` + Prometheus exporter.
* **Dashboards** Grafana folders *System*, *Agents*, *NATS*.
* **Alerts** PrometheusRule CRDs: crash‑loop, NATS backlog.

---

## 10 Configuration & Secrets

* Config via `config` crate — load order: `default.toml` → `<env>.toml` → env vars.
* Secrets: Kubernetes Secret (prod) or `.env` file (dev).

---

## 11 Deployment & CI/CD

| Stage            | Tool                                            |
| ---------------- | ----------------------------------------------- |
| Container build  | `cargo‑chef` multi‑stage Docker                 |
| Continuous build | GitHub Actions (`cargo test`, `cargo llvm‑cov`) |
| Delivery         | Argo CD sync to namespace `claude‑flow`         |
| Packaging        | Helm chart with NATS sub‑chart                  |

---

## 12 Development Environment

1. **Dev Container** Rust + NATS pre‑installed (`.devcontainer/`).
2. **VS Code Tasks** `Ctrl+Shift+B → run‑orchestrator` spins up NATS & orchestrator.
3. **Live reload** `cargo watch -x "run --bin orchestrator"`.

---

## 13 Testing & Benchmarking

* **Unit** `tokio::test`, `rstest`.
* **Integration** Docker Compose brings up NATS & Postgres.
* **Property‑based** `proptest` for serialization edge cases.
* **Performance** `criterion`; compare with `cargo‑benchcmp`.

---

## 14 Extensibility

| Scenario              | Hook                                                       |
| --------------------- | ---------------------------------------------------------- |
| New agent language    | Wrap in sidecar CLI, reuse NATS protocol.                  |
| Alternate message bus | Implement `BusAdapter` trait (NATS, Kafka, Redis Streams). |
| Cloud‑native secrets  | Enable AWS Secrets Manager feature in `config`.            |

---

## 15 Security

* Non‑root, read‑only containers.
* Claude API key injected at runtime, never baked into image.
* NATS accounts with scoped publish/subscribe ACLs.
* Supply‑chain hardening via `cargo deny`.

---

## 16 Glossary

| Term         | Definition                                         |
| ------------ | -------------------------------------------------- |
| Agent        | Claude CLI child process executing tasks.          |
| Group        | Logical collection of agents sharing a task scope. |
| Orchestrator | Rust service that spawns and coordinates agents.   |
| Subject      | NATS topic string for routing messages.            |

---

## 17 Appendices

### A Baseline `Cargo.toml`

```toml
[dependencies]
tokio = { version = "1.38", features = ["full", "process"] }
axum  = "0.8"
tonic = { version = "0.11", features = ["tls"] }
async-nats = "0.34"
sqlx = { version = "0.7", features = ["postgres", "runtime-tokio-rustls", "macros"] }
expectrl = { version = "0.7", features = ["async"] }
notify = "8"
tracing = "0.1"
tracing-opentelemetry = "0.31"
opentelemetry-otlp = "0.30"
```

### B Environment Variables

| Variable         | Example                               | Description           |
| ---------------- | ------------------------------------- | --------------------- |
| `NATS_URL`       | `nats://localhost:4222`               | Message‑bus endpoint. |
| `DATABASE_URL`   | `postgres://user:pw@localhost/claude` | Postgres DSN.         |
| `CLAUDE_API_KEY` | `sk‑…`                                | Secret Anthropic key. |
| `RUST_LOG`       | `info,claude=debug`                   | Log filter.           |

---

> *Agents: use this framework as the canonical source. Fill placeholders with diagrams, configs, and code as the system matures.*
