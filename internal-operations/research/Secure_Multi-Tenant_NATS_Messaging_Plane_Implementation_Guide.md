# Secure Multi-Tenant NATS Messaging Plane Implementation Guide

## NATS Accounts and Scoped Permissions

**Use Accounts for Tenant Isolation:** Leverage NATS **accounts** to isolate each tenant’s messaging namespace. Accounts provide true multi-tenancy – messages published in one account are invisible to any other account by default. This avoids complex subject prefix schemes for tenant separation. For example, two accounts `A` and `B` can both use subject `events.data` internally without conflict or cross-talk.

**Scoped Publish/Subscribe Permissions:** Within each account (or if using a single account for all tenants, which is **not recommended**), apply fine-grained ACLs so clients only access authorized subjects. NATS supports per-user allow/deny rules on subjects. For instance, you might allow a tenant’s agent user to **publish/subscribe only to their group prefix** (e.g. `groupA.>`), while denying access to anything else. In NATS config or JWT, this is expressed as permissions on the user. An example ACL for a restricted user versus an admin:

```json
{
  "users": [
    {
      "user": "admin",
      "password": "...",
      "permissions": {
        "publish": [ ">" ],
        "subscribe": [ ">" ]
      }
    },
    {
      "user": "tenantA_bot",
      "password": "...",
      "permissions": {
        "publish": { "allow": ["tenantA.>"] },
        "subscribe": { "allow": ["tenantA.>"] }
      }
    }
  ]
}
```

In this example, **`admin`** can publish/subscribe to everything (`">"` is a wildcard), whereas **`tenantA_bot`** is confined to subjects under `tenantA.`. By default, any subject not explicitly allowed is denied. You can similarly set **deny rules** to exclude patterns – e.g. `deny: ">"` for publish would make a user read-only. Use the principle of least privilege: limit each agent or service to only the topics it needs. (Note: If each tenant has its own account, wildcard `>` within that account only sees that tenant’s subjects, preserving isolation by design.)

**nsc Automation:** Use the NATS `nsc` tool to manage these credentials in a reproducible way. For example, to create accounts and users with scoped permissions and limits:

```bash
# Create a tenant account and enable JetStream with limits (see JetStream section)
nsc add account --name tenantA 
nsc edit account --name tenantA --js-mem-storage 512M --js-disk-storage 1G --js-streams 10 --js-consumer 50

# Add a user for the tenant with pub/sub restricted to tenant's subjects
nsc add user --name agent1 -a tenantA \
  --allow-pub "tenantA.*" \
  --allow-sub "tenantA.*"
```

The above ensures **tenantA**’s account can only use up to 512MB memory / 1GB disk of JetStream storage and at most 10 streams, 50 consumers, and the user *agent1* can only publish/subscribe to subjects matching `tenantA.*`.

## mTLS Networking: Single Cluster vs. Leaf Nodes

**Single-Cluster mTLS:** In a single NATS 2.10 cluster, enable TLS for all client and inter-node communications. Configure the NATS server with a **TLS block** requiring mutual auth (client cert verification) on client connections. For example:

```hocon
# nats-server.conf
listen: 0.0.0.0:4222
tls {
  cert_file: "./certs/nats-server.crt"
  key_file:  "./certs/nats-server.key"
  ca_file:   "./certs/ca.crt"
  verify: true            # require client certs (mTLS):contentReference[oaicite:8]{index=8}
}
cluster {
  listen: 0.0.0.0:4244
  tls {
    cert_file: "./certs/cluster.crt"
    key_file:  "./certs/cluster.key"
    ca_file:   "./certs/ca.crt"
  }
  routes = [ "nats://<other-node>:4244" ]
}
```

In the above, **`verify: true`** forces clients to present a cert signed by your CA. All servers use a common CA, and **route connections** (NATS server-to-server) are also secured with mutual TLS. Each NATS server has a certificate for cluster identity; the cluster TLS config ensures servers verify each other’s certs in both directions (full TLS handshake for routes). Clients must trust the CA and present their own cert/key for the server to verify. This approach keeps a **single trust domain** – simpler to manage if all agents are within one org or environment.

**Leafnode mTLS Option:** If you need to **extend across trust or network boundaries**, consider NATS *Leaf Nodes*. A leaf node is a separate NATS server (or cluster) that connects into a hub cluster. Each leaf connection can bind to a specific account on the hub. You could give each tenant or environment its own NATS server/cluster (with its own local TLS config/CA) and use leafnodes to link them to the main cluster. Leafnode connections also support TLS with verification on both sides. This yields *strong isolation*: e.g., a tenant’s leafnode could enforce its own auth and limits, and only permitted subjects flow to the core cluster (controlled via account exports/imports). However, this adds complexity. For the described use case (local Docker now, migrating to EKS), a single cluster with multi-account isolation is likely sufficient initially, unless you need that extra segregation or geographic distribution.

**Certificate Generation Script:** Below is a sample OpenSSL script to set up a minimal internal CA and issue a server and client certificate for mTLS:

```bash
# 1. Generate a self-signed CA (certificate authority)
openssl genrsa -out ca.key 4096
openssl req -x509 -new -sha256 -days 3650 -key ca.key -subj "/CN=NATS CA" -out ca.crt

# 2. Create NATS server cert signed by CA
openssl genrsa -out nats-server.key 2048
openssl req -new -key nats-server.key -subj "/CN=nats-server" -out nats-server.csr
openssl x509 -req -sha256 -days 1000 -in nats-server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out nats-server.crt

# 3. Create a client cert for NATS clients (agents) signed by same CA
openssl genrsa -out client.key 2048
openssl req -new -key client.key -subj "/CN=agent-client" -out client.csr
openssl x509 -req -sha256 -days 1000 -in client.csr -CA ca.crt -CAkey ca.key -CAserial ca.srl -out client.crt
```

This produces `ca.crt` (shared trust anchor), `nats-server.crt/key` for the NATS server, and a `client.crt/key` for an agent. Distribute `ca.crt` to all clients and configure servers/clients to trust it. In production, use a secure CA (or an internal PKI) and rotate certificates regularly.

**Rust Client TLS Setup:** Using the **`async-nats`** client, you can enforce TLS and provide the certs as follows:

```rust
use async_nats::ConnectOptions;
use tokio::time::Duration;

let nc = ConnectOptions::new()
    .require_tls(true)
    .add_root_certificates("ca.crt".into())                     // trust our CA
    .add_client_certificate("client.crt".into(), "client.key".into())  // present client cert
    .ping_interval(Duration::from_secs(10))
    .connect("tls://nats.example.com:4222")  // NATS URI with TLS
    .await?;
```

This connects to NATS with TLS enabled, using the CA and client certificate for mTLS authentication. (The server must have `verify` or `verify_and_map` enabled to require the cert.) The `.ping_interval(…)` is optional tuning. In **Kubernetes/EKS**, you would mount the certs as files in the pod and point the client to their paths. If using leafnodes, configure both sides with `tls { handshake_first: true }` in NATS 2.10+ so the TLS handshake occurs before any cluster INFO exchange.

## JetStream Resource Limits and Quotas

**Enable JetStream per Account:** JetStream (NATS persistence) can be **restricted on a per-account basis**. In the server config, enable JetStream globally and then turn it on for specific accounts with defined limits. For example:

```hocon
jetstream {
  max_mem: 1G, max_file: 100G, store_dir: "/data/nats/jetstream"
}

accounts: {
  TENANT_A: {
    jetstream: {
      max_mem: 512M,
      max_file: 1G,
      max_streams: 10,
      max_consumers: 100
    },
    users: [ { user: "tenantA_bot", password: "..." } ]
  },
  TENANT_B: {
    jetstream: { … }, 
    users: [ { user: "tenantB_bot", password: "..." } ]
  }
}
```

Here, **TENANT\_A** is allocated up to 512 MB memory and 1 GB file storage, and can create at most 10 streams and 100 consumers. This ensures one tenant cannot exhaust the entire cluster’s capacity. If you omit explicit limits, an enabled account can consume all available resources.  Use `nsc` in operator mode to set these limits in the account JWT as well (same fields: `--js-mem-storage`, `--js-disk-storage`, `--js-streams`, `--js-consumer`).

**Per-Stream Quotas:** When defining each JetStream **Stream**, configure limits like maximum messages, bytes, and message age. For example, an agent’s stream configuration (via NATS CLI or the Rust client) could include `max_bytes` (total storage per stream), `max_msgs` (number of messages), and `max_age` (time-to-live). This prevents uncontrolled growth of a stream. You could also use **discard policies** (e.g. discard old when full) to enforce circular buffers. Keep these quotas modest per tenant. For instance, if each agent only needs the last N events, set `max_msgs` or `max_bytes` accordingly.

**Deny Rules:** In addition to storage limits, apply **subject deny/allow rules** to JetStream APIs if needed. By default, if an account has JetStream enabled, it can create streams on any subject within its own account. If you want to prevent misuse of certain subjects (e.g. internal or reserved ones), you can leverage the account’s import/export controls or user permissions. For example, you might **deny publish on `$SYS.>` or JetStream event subjects** for non-admin users. In JWT-based config, `--deny-pub` or `--deny-sub` flags can set account-wide default denials. There’s also a global limit on JetStream API requests in flight (default 10k) to prevent overload – in huge deployments you can tune `request_queue_limit` to throttle aggressive clients.

**Connection Limits:** To avoid any one tenant overwhelming the service, set **connection and subscription limits** per account. For example, `nsc edit account --name tenantA --conns 100 --subscriptions 1000` would cap Tenant A to 100 active client connections and 1000 total subscriptions (across all clients) – preventing infinite spawn of agents or subscriptions. Likewise, `--data` can cap total data egress per account, and `--payload` can restrict the max size of any single message. These limits, combined with NATS server’s own `max_connections` (global default 65536) and **slow-consumer** detection, form a defensive shield against DoS scenarios.

## Secret Injection and Key Management

**Dev vs Prod Secrets:** In local development (Docker or VS Code dev container), use a simple **`.env` file** to supply secrets like the Claude API key. For example, a `.env` might contain:

```bash
CLAUDE_API_KEY=<your-claude-key>
NATS_USER=tenantA_bot
NATS_PASS=<nats-password>
```

The application can load this into environment variables at startup. **Never commit** the `.env` to source control – instead, use a template or sample file for reference.

In production (Kubernetes), use **Kubernetes Secrets** to inject sensitive values. You would create a K8s `Secret` object (base64-encoded values) and reference it in your Pod spec. For instance:

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: orchestrator-secrets
type: Opaque
data:
  CLAUDE_API_KEY: <Base64-of-key>
  NATS_CREDS: <Base64-of-creds-file> 
---
apiVersion: apps/v1
kind: Deployment
metadata: …
spec:
  template:
    spec:
      containers:
      - name: orchestrator
        image: myorg/agent-orch:latest
        env:
        - name: CLAUDE_API_KEY
          valueFrom:
            secretKeyRef: { name: orchestrator-secrets, key: CLAUDE_API_KEY }
        - name: NATS_CREDS
          valueFrom:
            secretKeyRef: { name: orchestrator-secrets, key: NATS_CREDS }
```

This way, the Claude API key and NATS credentials are provided via environment variables in the container. The secrets can be updated in Kubernetes and then **hot-reloaded** by the app (more on rotation below). Make sure RBAC rules in K8s restrict who can read these secrets, and consider enabling at-rest encryption for Secrets in the cluster.

**Pipeline Integration:** For CI/CD, avoid embedding secrets in pipelines. Instead, use your CI’s secret store or K8s integration to supply them at deploy time. For example, GitHub Actions can pull from repository secrets or HashiCorp Vault (if set up) to populate manifests. Since you’re starting without Vault, a straightforward approach is to keep using K8s Secrets and mount them. As you move to EKS, you might also explore AWS Secrets Manager or Parameter Store with the [Kubernetes External Secrets](https://external-secrets.io) operator, which can sync external secret stores into K8s secrets.

**Vault vs SealedSecrets vs SOPS (GitOps):** As your secret management matures, you have options:

* **HashiCorp Vault:** A centrally managed secrets server accessed via API. Vault provides dynamic secrets (e.g. it can generate short-lived tokens/credentials on demand) and fine-grained access control and audit. It’s powerful for **automated key rotations** and on-demand credentials, but requires operating the Vault service and integrating it with your apps. Vault shines for *dynamic* secrets and centralized policy enforcement, at the cost of added complexity and runtime dependency.

* **Bitnami SealedSecrets:** A Kubernetes-native solution for **encrypting secrets for Git**. You encrypt a Secret with the cluster’s public key; it creates a `SealedSecret` manifest that can be safely committed to git. Only your cluster’s controller can decrypt it. This is great for a GitOps workflow on K8s – secrets reside in git but in encrypted form. It’s easy for developers (encryption can be done with a CLI or IDE plugin), and decryption happens only in cluster. However, SealedSecrets ties secrets to a specific cluster (each has its own key), and doesn’t support dynamic rotation by itself (you’d replace and re-seal to rotate).

* **Mozilla SOPS:** A tool for encrypting arbitrary YAML/JSON documents (often used for K8s secrets or config files) using keys like PGP, KMS (AWS KMS, GCP KMS), or age. SOPS integrates well with GitOps tooling (Flux CD, Kustomize Controller, etc. can decrypt on-the-fly). It allows storing encrypted secrets in your git repo but requires managing encryption keys. It’s more flexible (cloud KMS support, file format agnostic) and has a strong community, but slightly higher setup overhead (developers need the encryption keys locally to encrypt/decrypt).

In summary, **Vault** is best for dynamic and runtime secrets with strong security assurances (API access at runtime), **SealedSecrets** and **SOPS** are better for static configuration secrets in GitOps (encrypted at rest in git). For now, using K8s Secrets (possibly with SealedSecrets or SOPS to check them into git securely) is a sound approach. For example, one could encrypt the `CLAUDE_API_KEY` value using SOPS and commit the file, avoiding plain text in repo. The choice depends on your workflow: Vault offers live secret delivery and rotation, while SealedSecrets/SOPS offer **GitOps-friendly encryption**. As you have **no Vault yet** and a relatively static key (Claude API key), starting with K8s Secrets + either SealedSecrets or SOPS for safe handling is reasonable, and you can adopt Vault later when automation needs grow.

## Threat Model and Mitigations

Below is an **Attack → Mitigation** matrix addressing potential threats like token leaks, wildcard abuse, and DoS from runaway agents:

| **Threat Scenario**                                                                                  | **Mitigation Measures**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| ---------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **API Key Leakage** (Claude key or NATS creds exposed)                                               | - **Least Exposure:** Load secrets via env variables or files not baked into images. Avoid logging or echoing secrets. <br/>- **K8s Secret Controls:** Restrict Secret access with RBAC; enable secret encryption at rest. <br/>- **Ephemeral Credentials:** Use short-lived tokens where possible (e.g. NATS JWT with expiration). <br/>- **Rotation:** Regularly rotate secrets (see rotation workflow below) to limit the window of compromise.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| **Wildcard Subscription Abuse** (tenant subscribes to `>` or unauthorized subjects)                  | - **Account Isolation:** Separate tenants into different NATS accounts so a wildcard only sees that account’s subjects. <br/>- **ACL Restrictions:** For shared or multi-use accounts, explicitly disallow broad wildcards. E.g., user permissions allowing only `groupX.>` and not `>`. <br/>- **Monitoring & Auditing:** Log or detect if any client attempts to subscribe to subjects outside their allowed scope. Use NATS server warnings for unauthorized sub requests. <br/>- **Private Replies:** Utilize NATS **\_INBOX.** reply subjects and `allow_responses` permissions so that services can only respond to requests they receive, and cannot open a wildcard to snoop on others’ replies.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| **DoS via Unbounded Spawns** (rogue agent opens huge number of connections, subs or floods messages) | - **Connection Limits:** Impose max connection count per account/user (`--conns` limit via nsc) to prevent unlimited client instances. <br/>- **Subscription Limits:** Cap subscriptions per account (`--subscriptions` limit) so one tenant can’t register millions of subjects. <br/>- **Rate Limits & Quotas:** Use JetStream limits (memory, storage, streams) per account so resource use is contained. Optionally, message size limits (`--payload`) to prevent giant payload attacks. <br/>- **Backpressure in Client:** Implement bounded queues/channels in your Rust consumers. For example, use `ConnectOptions::subscription_capacity(N)` to bound the message buffer per subscription and get slow-consumer signals. Your Tokio select loop should use limited-size channels or tokio’s `sync::mpsc` with capacity to avoid unlimited buffering. <br/>- **Spawn Control:** In the agent orchestrator, place limits on how many tasks or child agents can be spawned per tenant request. This can be a simple semaphore or counter check to refuse or delay new spawns when capacity is exhausted. Also consider using NATS JetStream consumer message rates (pull consumers can be tuned) if flooding is a concern. <br/>- **Monitoring & Autoscale:** Monitor for unusual spikes in messages or connections. NATS server provides monitoring endpoints (HTTP or via `$SYS.ACCOUNT` events) – feed these to alerts. If one tenant is overloading (possible DoS attempt), you could temporarily throttle or isolate them (e.g. isolate their account to a leafnode or apply **lame-duck mode** to their connections to gradually drain them). |

*(Additional threats like **mTLS certificate misuse** or **JWT tampering** can be handled by always verifying client certs and using JWT signatures – since you’re using mTLS, only clients with valid certs (and correct account JWT if applicable) can connect. Also ensure NATS auth is turned on (no anonymous connections) even with TLS.)*

## Claude API Key Rotation Workflow (Zero-Downtime)

To safely rotate the single global Claude API key without downtime, use a **staged state machine** approach. The orchestrator process will catch a signal to reload its config. Below is a state chart of the rotation process:

1. **State: KEY\_A\_IN\_USE** – The system is running with the current Claude API key (Key A) active. All requests are signed with Key A, which is stored in memory from an env var or mounted secret.

2. **Event: New Key Issued** – You obtain a new Claude API key (Key B) from the provider (e.g. Claude console or API). At this point, Key B is not yet in use by the system.

3. **State: STAGING\_NEW\_KEY** – Load Key B into the environment **without removing Key A**. For example, update the Kubernetes Secret or `.env` file with the new key **alongside the old key**, or prepare it in a staging variable. (If using K8s, you might update the secret with *both* keys or a new secret and mount it.) Ensure the application can access Key B (but it should still be using Key A until instructed to switch).

4. **Event: SIGHUP Signal** – Trigger the orchestrator to reload config (e.g. `kill -HUP <pid>` for a Unix signal, or in Kubernetes, you might use `kubectl exec` or a sidecar to send the signal to the pod). The Rust application should have a signal handler (via `tokio::signal::unix::signal(SIGHUP)`) that sets a flag or initiates config reload.

5. **Transition: Reload Config** – On SIGHUP, the orchestrator reads the updated secret (e.g. re-read the env var or file where Key B is now present). It **switches its in-memory API key** from A to B. Ideally, log an info message that a key rotation happened (but do not log the key). Existing Claude requests should start using Key B for new calls. (Any in-flight requests started with Key A can be allowed to complete, if possible, to avoid interruption.)

6. **State: KEY\_B\_IN\_USE** – The orchestrator is now using Key B for all outgoing requests. Key A is no longer actively used by the running process.

7. **Event: Revoke Key A** – Once confident that all nodes have reloaded and are using Key B (and Key A is no longer needed – e.g. after a safety period or after confirming via metrics that no requests are hitting with Key A), you should invalidate Key A. This could mean deleting it from the Claude provider’s console or marking it as rotated. Also remove it from anywhere it was stored (update the K8s Secret to delete the old key, or if both keys were present, remove the old). This ensures a leaked or old key can’t be abused.

8. **State: KEY\_B\_ACTIVE (Post-Rotation)** – Only Key B remains in use and known. The system continues operation with no restart downtime. The next rotation will repeat the process with a new key (Key C, and so on).

**Workflow Summary:** *Generate new key → deploy new key to config → signal reload → atomically switch keys in memory → decommission old key.* By using a SIGHUP reload, you avoid restarting the whole service or cluster. The key to zero downtime is that the process keeps running and simply swaps credentials on the fly. In Kubernetes, this can be orchestrated by updating the Secret (which won’t automatically refresh in running pods, so you either use an external trigger like SIGHUP or run an init that checks periodically). Some setups use a sidecar container or watcher that detects secret changes and sends SIGHUP to the main container. Since you have **one global key** shared by the orchestrator, the rotation is “big bang” – all agents switch at once. Ensure that the new key is tested (perhaps in a staging environment or with a test request) before switching, and consider building a small “health check” that after reload, tries a trivial Claude API call to verify Key B’s validity. If it fails, you can fall back to Key A quickly.

Finally, document and **automate this rotation** procedure. For example, a cronjob or pipeline could fetch a new key monthly, update the secret, and trigger the reload. This reduces the chance of human error. By structuring the rotation as above, you’ve minimized downtime to virtually zero – ongoing NATS message handling by agents is unaffected during key reload, and Claude requests simply start using the new credentials immediately after the signal.
