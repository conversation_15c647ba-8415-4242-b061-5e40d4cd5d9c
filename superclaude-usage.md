# SuperClaude – Development Framework for Claude Code

## 💡 Core Capabilities

### 🧠 **Cognitive Personas (Now as Flags!)**
Switch between different approaches with persona flags:

```bash
/analyze --code --persona-architect     # Systems thinking approach
/build --react --persona-frontend       # UX-focused development  
/scan --security --persona-security     # Security-first analysis
/troubleshoot --prod --persona-analyzer # Root cause analysis approach
```

### ⚡ **19 Commands**
Development lifecycle coverage:

**Development Commands**
```bash
/build --react --magic --tdd    # Development with AI components
/dev-setup --ci --monitor       # Environment setup
/test --coverage --e2e --pup    # Testing strategies
```

**Analysis & Quality**
```bash
/review --quality --evidence --persona-qa     # AI-powered code review
/analyze --architecture --seq   # System analysis
/troubleshoot --prod --five-whys # Issue resolution
/improve --performance --iterate # Optimization
/explain --depth expert --visual # Documentation
```

**Operations & Security**
```bash
/deploy --env prod --plan       # Deployment planning
/scan --security --owasp --deps # Security audits
/migrate --dry-run --rollback   # Database migrations
/cleanup --all --validate       # Maintenance tasks
```

### 🎛️ **MCP Integration**
- **Context7**: Access to library documentation
- **Sequential**: Multi-step reasoning capabilities
- **Magic**: AI-generated UI components
- **Puppeteer**: Browser testing and automation

**⚠️ Important:** SuperClaude does not include MCP servers. You need to install them separately in Claude Code's MCP settings to use MCP-related flags (--c7, --seq, --magic, --pup).

### 📊 **Token Efficiency**
SuperClaude's @include template system helps manage token usage:
- **UltraCompressed mode** option for token reduction
- **Template references** for configuration management
- **Caching mechanisms** to avoid redundancy
- **Context-aware compression** options

## 🎮 Example Workflows

### Enterprise Architecture Flow
```bash
/design --api --ddd --bounded-context --persona-architect    # Domain-driven design
/estimate --detailed --worst-case --seq                      # Resource planning
/scan --security --validate --persona-security               # Security review
/build --api --tdd --coverage --persona-backend              # Implementation
```

### Production Issue Resolution
```bash
/troubleshoot --investigate --prod --persona-analyzer        # Analysis
/analyze --profile --perf --seq                             # Performance review
/improve --performance --threshold 95% --persona-performance # Optimization
/test --integration --e2e --pup                             # Validation
```

### Framework Troubleshooting & Improvement
```bash
/troubleshoot --introspect                                  # Debug SuperClaude behavior
/analyze --introspect --seq                                 # Analyze framework patterns
/improve --introspect --uc                                  # Optimize token usage
```

### Full-Stack Feature Development
```bash
/build --react --magic --watch --persona-frontend           # UI development
/test --coverage --e2e --strict --persona-qa                # Quality assurance
/scan --validate --deps --persona-security                  # Security check
```

## 🎭 Available Personas

| Persona | Focus Area | Tools | Use Cases |
|---------|-----------|-------|-----------|
| **architect** | System design | Sequential, Context7 | Architecture planning |
| **frontend** | User experience | Magic, Puppeteer, Context7 | UI development |
| **backend** | Server systems | Context7, Sequential | API development |
| **security** | Security analysis | Sequential, Context7 | Security reviews |
| **analyzer** | Problem solving | All MCP tools | Debugging |
| **qa** | Quality assurance | Puppeteer, Context7 | Testing |
| **performance** | Optimization | Puppeteer, Sequential | Performance tuning |
| **refactorer** | Code quality | Sequential, Context7 | Code improvement |
| **mentor** | Knowledge sharing | Context7, Sequential | Documentation |

## 🛠️ Configuration Options

### Thinking Depth Control
```bash
# Standard analysis
/analyze --think

# Deeper analysis  
/design --think-hard

# Maximum depth
/troubleshoot --ultrathink
```

### Introspection Mode
```bash
# Enable self-aware analysis for SuperClaude improvement
/analyze --introspect

# Debug SuperClaude behavior
/troubleshoot --introspect --seq

# Optimize framework performance
/improve --introspect --persona-performance
```

### Token Management
```bash
# Standard mode
/build --react --magic

# With compression
/analyze --architecture --uc

# Native tools only
/scan --security --no-mcp
```

### Evidence-Based Development
SuperClaude encourages:
- Documentation for design decisions
- Testing for quality improvements
- Metrics for performance work
- Security validation for deployments
- Analysis for architectural choices

## 📋 Command Categories

### Development (3 Commands)
- `/build` - Project builder with stack templates
- `/dev-setup` - Development environment setup
- `/test` - Testing framework

### Analysis & Improvement (5 Commands)
- `/review` - AI-powered code review with evidence-based recommendations
- `/analyze` - Code and system analysis
- `/troubleshoot` - Debugging and issue resolution
- `/improve` - Enhancement and optimization
- `/explain` - Documentation and explanations

### Operations (6 Commands)
- `/deploy` - Application deployment
- `/migrate` - Database and code migrations
- `/scan` - Security and validation
- `/estimate` - Project estimation
- `/cleanup` - Project maintenance
- `/git` - Git workflow management

### Design & Workflow (5 Commands)
- `/design` - System architecture
- `/spawn` - Parallel task execution
- `/document` - Documentation creation
- `/load` - Project context loading
- `/task` - Task management

## 🔧 Technical Architecture v2

SuperClaude v2's architecture enables extensibility:

**🏗️ Modular Configuration**
- **CLAUDE.md** – Core configuration with @include references
- **.claude/shared/** – Centralized YAML templates
- **commands/shared/** – Reusable command patterns
- **@include System** – Template engine for configuration

**🎯 Unified Command System**
- **19 Commands** – Development lifecycle coverage
- **Flag Inheritance** – Universal flags on all commands
- **Persona Integration** – 9 cognitive modes as flags
- **Template Validation** – Reference integrity checking

**📦 Architecture Benefits**
- **Single Source of Truth** – Centralized updates
- **Easy Extension** – Add new commands/flags
- **Consistent Behavior** – Unified flag handling
- **Reduced Duplication** – Template-based configuration

**✅ Quality Features**
- **Evidence-Based Approach** – Documentation encouraged
- **Research Integration** – Library documentation access
- **Error Recovery** – Graceful failure handling
- **Structured Output** – Organized file locations

## 📊 Comparison

| Aspect | Standard Claude Code | SuperClaude Framework |
|--------|---------------------|----------------------|
| **Expertise** | General responses | 9 specialized personas |
| **Commands** | Manual instructions | 19 workflow commands |
| **Context** | Session-based | Git checkpoint support |
| **Tokens** | Standard usage | Compression options |
| **Approach** | General purpose | Evidence-based |
| **Documentation** | As needed | Systematic approach |
| **Quality** | Variable | Validation patterns |
| **Integration** | Basic tools | MCP orchestration |